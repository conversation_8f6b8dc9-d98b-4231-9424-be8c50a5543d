/* 導入工序項目樣式 */
@import './ProcessItem.css';

.process-preparation-container {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  padding: 24px;
}

.process-header {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  margin-bottom: 24px;
  gap: 16px;
}

.process-header h2 {
  flex-grow: 1;
  margin: 0;
  font-size: 20px;
  color: #333;
}

.back-to-bom {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  color: #3b82f6;
  font-weight: 500;
}

.back-to-bom:hover {
  text-decoration: underline;
}

.add-btn, .import-btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 6px;
  background-color: #3b82f6;
  color: white;
}

.add-btn:hover, .import-btn:hover {
  background-color: #2563eb;
}

.add-btn:disabled {
  background-color: #93c5fd;
  cursor: not-allowed;
  opacity: 0.6;
}

.clear-btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 6px;
  background-color: #ef4444;
  color: white;
  margin-right: 10px;
}

.clear-btn:hover {
  background-color: #dc2626;
}

.read-only-notice {
  background-color: #fef3c7;
  border: 1px solid #f59e0b;
  border-radius: 6px;
  padding: 12px 16px;
  margin-bottom: 16px;
}

.read-only-notice p {
  color: #92400e;
  font-weight: 500;
  margin: 0;
  display: flex;
  align-items: center;
}

.read-only-notice p::before {
  content: '\f071'; /* 警告圖標的 Unicode */
  font-family: 'Font Awesome 5 Free';
  font-weight: 900;
  margin-right: 8px;
  color: #f59e0b;
}

/* 流程圖樣式 */
.process-flow-container {
  margin-top: 20px;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  overflow: hidden;
}

.process-flow-header {
  display: flex;
  background-color: #f8fafc;
  border-bottom: 1px solid #e2e8f0;
}

.process-column {
  flex: 1;
  padding: 12px;
  text-align: center;
  border-right: 1px dashed #cbd5e1;
}

.material-column {
  flex: 1;
  /* 移除特殊背景色，保持與其他欄一致 */
}

.process-column:last-child {
  border-right: none;
}

.process-column h3 {
  margin: 0;
  font-size: 16px;
  color: #334155;
  text-align: center;
}

.process-column h3 span {
  display: block;
  margin-top: 4px;
}

.process-flow-body {
  position: relative;
  min-height: 600px;
  display: flex;
}

.position-import-hint {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  padding: 20px;
  background-color: #f8fafc;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.position-import-hint p {
  margin-bottom: 16px;
  color: #64748b;
  font-size: 16px;
}

.process-flow-content {
  display: flex;
  width: 100%;
  position: relative;
  min-height: 500px;
}

.position-items {
  display: flex;
  flex-direction: column;
  gap: 10px;
  padding: 15px 5px;
}

.position-item {
  background-color: #f1f5f9;
  border: 1px solid #cbd5e1;
  border-radius: 4px;
  padding: 8px 6px;
  text-align: center;
  cursor: pointer;
  position: relative;
  min-width: 50px;
  max-width: 120px;
  font-size: 0.9em;
  color: #64748b;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  transition: all 0.2s ease;
  margin: 0 auto;
}

.material-item {
  /* 移除特殊樣式，保持與其他項目一致 */
  max-width: 120px;
  text-align: center;
}

.position-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  background-color: #e2e8f0;
}

.position-item.selected {
  background-color: #cbd5e1;
  border-color: #94a3b8;
  color: #334155;
  box-shadow: 0 0 0 2px rgba(148, 163, 184, 0.5);
}

.material-has-process {
  position: absolute;
  top: -4px;
  right: -4px;
  width: 14px;
  height: 14px;
  background: #b0b0b0;
  border-radius: 50%;
  z-index: 2;
  border: 2px solid #fff;
  box-shadow: 0 1px 2px rgba(0,0,0,0.08);
}
.position-item {
  position: relative;
}

/* 連接線樣式 */
.process-connections {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  pointer-events: none;
  z-index: 5;
}

/* 固定工序連接線樣式 */
.connection-lines {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 99;
}

.connection-svg {
  width: 100%;
  height: 100%;
}

.connection-line {
  stroke: #2563eb;
  stroke-width: 3;
  fill: none;
  filter: drop-shadow(0 1px 2px rgba(37, 99, 235, 0.3));
}

.connection-label {
  font-family: 'Arial', sans-serif;
  font-weight: bold;
  text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8);
  pointer-events: none;
}

/* 連接線顯示 */
.process-connection-line {
  pointer-events: none;
}

.process-connection-line path {
  fill: none;
  stroke: #94a3b8;
  stroke-width: 2;
  transition: all 0.3s ease;
}

.process-connection-line.highlighted path {
  stroke: #475569 !important;
  stroke-width: 3 !important;
}

/* 固定工序的連接線樣式 */
.process-item.fixed + .process-connection-line path,
.process-connection-line[data-source-id*="fixed"] path,
.process-connection-line[data-target-id*="fixed"] path,
.process-connection-line.highlighted path {
  stroke: #4f46e5 !important;
  stroke-width: 3 !important;
  stroke-dasharray: none !important;
}

/* 模態對話框樣式 */
.process-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.process-modal-content {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  width: 90%;
  max-width: 600px;
  max-height: 90vh;
  overflow-y: auto;
}

.process-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #e2e8f0;
}

.process-modal-header h3 {
  margin: 0;
  font-size: 18px;
  color: #1e293b;
  font-weight: 600;
}

.close-btn {
  background: none;
  border: none;
  font-size: 18px;
  cursor: pointer;
  color: #64748b;
}

.close-btn:hover {
  color: #334155;
}

.process-modal-body {
  padding: 20px;
}

.form-group {
  margin-bottom: 16px;
}

.form-group label {
  display: block;
  margin-bottom: 6px;
  font-weight: 500;
  color: #1e293b;
  font-size: 14px;
}

.form-group .help-text {
  font-size: 12px;
  color: #64748b;
  margin-bottom: 6px;
  font-style: italic;
}

.form-group input,
.form-group select {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #cbd5e1;
  border-radius: 4px;
  font-size: 14px;
}

.form-group input:focus,
.form-group select:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 1px #93c5fd;
}

.required {
  color: #ef4444;
  margin-left: 2px;
}

.sequence-number {
  background-color: #f1f5f9;
  border: 1px solid #cbd5e1;
  border-radius: 4px;
  padding: 8px 12px;
  font-weight: 600;
  color: #334155;
  font-family: monospace;
  letter-spacing: 1px;
  text-align: center;
}

.form-row {
  display: flex;
  gap: 12px;
}

.form-row .form-group {
  flex: 1;
}

.form-actions {
  display: flex;
  justify-content: space-between;
  margin-top: 24px;
}

.left-buttons {
  display: flex;
  gap: 10px;
}

.right-buttons {
  display: flex;
  align-items: center;
  gap: 10px;
}

.button-separator {
  width: 1px;
  height: 24px;
  background-color: #e5e7eb;
  margin: 0 4px;
}

.stage-complete-btn, .stage-incomplete-btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 6px;
  background-color: #3b82f6;
  color: white;
}

.stage-complete-btn {
  background-color: #10b981;
}

.stage-complete-btn:hover {
  background-color: #059669;
}

.stage-incomplete-btn {
  background-color: #f59e0b;
}

.stage-incomplete-btn:hover {
  background-color: #d97706;
}

.save-btn,
.delete-btn,
.unmerge-btn,
.cancel-btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 6px;
}

.save-btn {
  background-color: #3b82f6;
  color: white;
}

.save-btn:hover {
  background-color: #2563eb;
}

.delete-btn {
  background-color: #ef4444;
  color: white;
}

.delete-btn:hover {
  background-color: #dc2626;
}

.unmerge-btn {
  background-color: #f59e0b;
  color: white;
}

.unmerge-btn:hover {
  background-color: #d97706;
}

.cancel-btn {
  background-color: #e2e8f0;
  color: #334155;
}

.cancel-btn:hover {
  background-color: #cbd5e1;
}

/* 半透明效果 */
.process-item.faded,
.process-connection-line.faded {
  opacity: 0.4;
}

/* 多選模式樣式 */
.multi-select-btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 6px;
  background-color: #f59e42;
  color: white;
  margin-right: 10px;
}
.multi-select-btn:hover {
  background-color: #ea580c;
}

.process-item.multi-selecting {
  border: 2px dashed #f59e42;
  background: #fff7ed;
  box-shadow: 0 0 0 2px #f59e4233;
}
.process-item.multi-selected {
  border: 2px solid #ea580c;
  background: #ffedd5;
  box-shadow: 0 0 0 2px #ea580c55;
}
.multi-checkbox {
  margin-right: 6px;
  accent-color: #f59e42;
  width: 18px;
  height: 18px;
  vertical-align: middle;
}
.multi-select-actions {
  display: flex;
  align-items: center;
  gap: 16px;
  margin: 18px 0 0 0;
  padding: 8px 0 8px 8px;
  background: #fff7ed;
  border: 1px solid #f59e42;
  border-radius: 6px;
}
.batch-add-btn {
  background: #f59e42;
  color: #fff;
  border: none;
  border-radius: 4px;
  padding: 8px 18px;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 15px;
}
.batch-add-btn:disabled {
  background: #fde68a;
  color: #b45309;
  cursor: not-allowed;
  opacity: 0.7;
}
.batch-add-btn:hover:not(:disabled) {
  background: #ea580c;
}
.multi-select-count {
  color: #ea580c;
  font-weight: 500;
  font-size: 15px;
}

/* 材料區多選模式樣式 */
.position-item.multi-selecting {
  /* 不做任何變化 */
}

.position-item.multi-selected {
  /* 不做任何變化 */
}

.position-item .multi-checkbox {
  margin-right: 6px;
  accent-color: #f59e42;
  width: 18px;
  height: 18px;
  vertical-align: middle;
  position: absolute;
  top: 4px;
  left: 4px;
  z-index: 10;
}

.position-item.disabled {
  opacity: 0.5;
  pointer-events: none;
  cursor: not-allowed;
}






