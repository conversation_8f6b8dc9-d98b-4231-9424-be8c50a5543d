import { ref, reactive } from 'vue';

const notification = reactive({
  show: false,
  type: 'info',
  title: '通知',
  message: '',
  isConfirm: false,
  onConfirm: null,
  onCancel: null
});

// 通知計時器
let notificationTimer = null;

// 顯示一般通知
export function showNotification(message, type = 'info', title = '通知', duration = 3000) {
  // 清除之前的計時器
  if (notificationTimer) {
    clearTimeout(notificationTimer);
    notificationTimer = null;
  }

  // 配置新的通知
  notification.show = true;
  notification.type = type;
  notification.title = title;
  notification.message = message;
  notification.isConfirm = false;
  notification.onConfirm = null;
  notification.onCancel = null;

  // 設置自動關閉計時器（非確認對話框才自動關閉）
  if (duration > 0) {
    notificationTimer = setTimeout(() => {
      notification.show = false;
    }, duration);
  }
}

// 顯示確認對話框
export function showConfirm(message, onConfirm, onCancel = null, title = '確認', type = 'warning') {
  // 清除之前的計時器
  if (notificationTimer) {
    clearTimeout(notificationTimer);
    notificationTimer = null;
  }

  // 配置新的確認對話框
  notification.show = true;
  notification.type = type;
  notification.title = title;
  notification.message = message;
  notification.isConfirm = true;
  notification.onConfirm = onConfirm;
  notification.onCancel = onCancel;
}

// 成功通知快捷方法
export function showSuccess(message, title = '成功', duration = 3000) {
  showNotification(message, 'success', title, duration);
}

// 錯誤通知快捷方法
export function showError(message, title = '錯誤', duration = 5000) {
  showNotification(message, 'error', title, duration);
}

// 警告通知快捷方法
export function showWarning(message, title = '警告', duration = 4000) {
  showNotification(message, 'warning', title, duration);
}

// 顯示提示框
export function showPrompt(message, onConfirm, onCancel = null, title = '請輸入', type = 'info') {
  // 清除之前的計時器
  if (notificationTimer) {
    clearTimeout(notificationTimer);
    notificationTimer = null;
  }

  // 配置新的提示框
  notification.show = true;
  notification.type = type;
  notification.title = title;
  notification.message = message;
  notification.isConfirm = true;
  notification.onConfirm = () => {
    const value = prompt(message, '');
    if (value !== null) {
      onConfirm(value);
    } else if (onCancel) {
      onCancel();
    }
  };
  notification.onCancel = onCancel || (() => {});
}

export function closeNotification() {
  notification.show = false;
}

// 使用通知的自定義hook
export function useNotification() {
  return {
    success: showSuccess,
    error: showError,
    warning: showWarning,
    info: showNotification,
    confirm: showConfirm,
    prompt: showPrompt,
    close: closeNotification
  };
}

export default notification;
