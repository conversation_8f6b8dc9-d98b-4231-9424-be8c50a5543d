/**
 * 歷史記錄管理模組
 * 用於管理工序編輯的歷史狀態，支援最多10個版本的保存和恢復
 */

class HistoryManager {
  constructor(maxHistorySize = 10) {
    this.maxHistorySize = maxHistorySize;
    this.history = [];
    this.currentIndex = -1;
    this.isNavigating = false; // 防止在導航時觸發新的歷史記錄
  }

  /**
   * 保存當前狀態到歷史記錄
   * @param {Object} state - 要保存的狀態對象
   * @param {string} description - 狀態描述
   */
  saveState(state, description = '') {
    // 如果正在導航中，不保存新狀態
    if (this.isNavigating) {
      return;
    }

    // 深拷貝狀態以避免引用問題
    const stateCopy = JSON.parse(JSON.stringify(state));
    
    // 創建歷史記錄項目
    const historyItem = {
      id: Date.now(),
      timestamp: new Date().toISOString(),
      description: description || `編輯狀態 ${this.history.length + 1}`,
      state: stateCopy
    };

    // 如果當前不在最新位置，移除後面的歷史記錄
    if (this.currentIndex < this.history.length - 1) {
      this.history = this.history.slice(0, this.currentIndex + 1);
    }

    // 添加新的歷史記錄
    this.history.push(historyItem);

    // 如果超過最大數量，移除最舊的記錄
    if (this.history.length > this.maxHistorySize) {
      this.history.shift();
    } else {
      this.currentIndex++;
    }

    // 確保 currentIndex 不超出範圍
    this.currentIndex = Math.min(this.currentIndex, this.history.length - 1);
  }

  /**
   * 後退到上一個狀態
   * @returns {Object|null} 上一個狀態，如果沒有則返回 null
   */
  goBack() {
    if (!this.canGoBack()) {
      return null;
    }

    this.isNavigating = true;
    this.currentIndex--;
    const state = this.history[this.currentIndex];
    
    // 延遲重置導航標誌，確保狀態更新完成
    setTimeout(() => {
      this.isNavigating = false;
    }, 100);

    return state;
  }

  /**
   * 前進到下一個狀態
   * @returns {Object|null} 下一個狀態，如果沒有則返回 null
   */
  goForward() {
    if (!this.canGoForward()) {
      return null;
    }

    this.isNavigating = true;
    this.currentIndex++;
    const state = this.history[this.currentIndex];
    
    // 延遲重置導航標誌，確保狀態更新完成
    setTimeout(() => {
      this.isNavigating = false;
    }, 100);

    return state;
  }

  /**
   * 跳轉到指定索引的狀態
   * @param {number} index - 目標索引
   * @returns {Object|null} 目標狀態，如果索引無效則返回 null
   */
  goToIndex(index) {
    if (index < 0 || index >= this.history.length) {
      return null;
    }

    this.isNavigating = true;
    this.currentIndex = index;
    const state = this.history[this.currentIndex];
    
    setTimeout(() => {
      this.isNavigating = false;
    }, 100);

    return state;
  }

  /**
   * 檢查是否可以後退
   * @returns {boolean}
   */
  canGoBack() {
    return this.currentIndex > 0;
  }

  /**
   * 檢查是否可以前進
   * @returns {boolean}
   */
  canGoForward() {
    return this.currentIndex < this.history.length - 1;
  }

  /**
   * 獲取當前狀態
   * @returns {Object|null}
   */
  getCurrentState() {
    if (this.currentIndex >= 0 && this.currentIndex < this.history.length) {
      return this.history[this.currentIndex];
    }
    return null;
  }

  /**
   * 獲取歷史記錄列表（用於顯示）
   * @returns {Array}
   */
  getHistoryList() {
    return this.history.map((item, index) => ({
      id: item.id,
      index: index,
      description: item.description,
      timestamp: item.timestamp,
      isCurrent: index === this.currentIndex
    }));
  }

  /**
   * 清除所有歷史記錄
   */
  clear() {
    this.history = [];
    this.currentIndex = -1;
    this.isNavigating = false;
  }

  /**
   * 獲取歷史記錄統計信息
   * @returns {Object}
   */
  getStats() {
    return {
      totalCount: this.history.length,
      currentIndex: this.currentIndex,
      canGoBack: this.canGoBack(),
      canGoForward: this.canGoForward(),
      maxSize: this.maxHistorySize
    };
  }

  /**
   * 從 JSON 數據恢復歷史記錄
   * @param {Object} data - 序列化的歷史數據
   */
  restore(data) {
    if (data && Array.isArray(data.history)) {
      this.history = data.history;
      this.currentIndex = data.currentIndex || -1;
      
      // 確保索引在有效範圍內
      if (this.currentIndex >= this.history.length) {
        this.currentIndex = this.history.length - 1;
      }
    }
  }

  /**
   * 序列化歷史記錄為 JSON
   * @returns {Object}
   */
  serialize() {
    return {
      history: this.history,
      currentIndex: this.currentIndex,
      maxHistorySize: this.maxHistorySize
    };
  }
}

export default HistoryManager;
