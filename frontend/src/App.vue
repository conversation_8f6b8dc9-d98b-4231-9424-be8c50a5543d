<script setup>
import AppNotification from './components/AppNotification.vue';
import notification from './services/notificationService';
import { onMounted } from 'vue';

onMounted(() => {
  // 確保fontawesome已加載
  if (!document.getElementById('font-awesome-css')) {
    const link = document.createElement('link');
    link.id = 'font-awesome-css';
    link.rel = 'stylesheet';
    link.href = 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.7.2/css/all.min.css';
    document.head.appendChild(link);
  }
});
</script>

<template>
  <router-view />
  <AppNotification 
    v-model:show="notification.show"
    :type="notification.type"
    :title="notification.title"
    :message="notification.message"
    :is-confirm="notification.isConfirm"
    @confirm="typeof notification.onConfirm === 'function' ? notification.onConfirm() : null"
    @cancel="typeof notification.onCancel === 'function' ? notification.onCancel() : null"
  />
</template>

<style>
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: 'PingFang TC', 'Microsoft JhengHei', '微軟正黑體', 'Helvetica Neue', Arial, sans-serif;
  background-color: #f8f9fa;
  color: #333;
  line-height: 1.6;
}

#app {
  width: 100%;
  height: 100vh;
}
</style>
