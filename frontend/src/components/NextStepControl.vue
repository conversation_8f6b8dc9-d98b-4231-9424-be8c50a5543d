<template>
  <div class="next-step-control">
    <!-- 主要控制按鈕 -->
    <div class="step-buttons">
      <button 
        class="step-btn back-btn" 
        @click="goBack" 
        :disabled="!canGoBack"
        :title="canGoBack ? '返回上一步' : '沒有上一步'"
      >
        <i class="fas fa-arrow-left"></i>
      </button>
      
      <button 
        class="step-btn forward-btn" 
        @click="goForward" 
        :disabled="!canGoForward"
        :title="canGoForward ? '前進下一步' : '沒有下一步'"
      >
        <i class="fas fa-arrow-right"></i>
      </button>
      
      <button 
        class="step-btn save-step-btn" 
        @click="saveCurrentStep"
        :title="'保存當前編輯狀態'"
      >
        <i class="fas fa-bookmark"></i>
      </button>
      
      <button 
        class="step-btn history-btn" 
        @click="toggleHistoryPanel"
        :title="'顯示/隱藏歷史記錄'"
        :class="{ active: showHistoryPanel }"
      >
        <i class="fas fa-history"></i>
      </button>
    </div>

    <!-- 狀態指示器 -->
    <div class="step-indicator" v-if="stats.totalCount > 0">
      <span class="step-text">
        {{ stats.currentIndex + 1 }} / {{ stats.totalCount }}
      </span>
    </div>

    <!-- 歷史記錄面板 -->
    <div class="history-panel" v-if="showHistoryPanel && historyList.length > 0">
      <div class="history-header">
        <h4>編輯歷史</h4>
        <button class="close-btn" @click="showHistoryPanel = false">
          <i class="fas fa-times"></i>
        </button>
      </div>
      
      <div class="history-list">
        <div 
          v-for="item in historyList" 
          :key="item.id"
          class="history-item"
          :class="{ current: item.isCurrent }"
          @click="goToStep(item.index)"
        >
          <div class="history-item-content">
            <div class="history-description">{{ item.description }}</div>
            <div class="history-time">{{ formatTime(item.timestamp) }}</div>
          </div>
          <div class="history-indicator" v-if="item.isCurrent">
            <i class="fas fa-arrow-right"></i>
          </div>
        </div>
      </div>
      
      <div class="history-actions">
        <button class="clear-history-btn" @click="clearHistory">
          <i class="fas fa-trash"></i> 清除歷史
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue';

// Props
const props = defineProps({
  historyManager: {
    type: Object,
    required: true
  },
  currentState: {
    type: Object,
    default: () => ({})
  }
});

// Emits
const emit = defineEmits([
  'state-changed',
  'save-step',
  'go-back',
  'go-forward',
  'go-to-step',
  'clear-history'
]);

// 響應式數據
const showHistoryPanel = ref(false);

// 計算屬性
const stats = computed(() => props.historyManager.getStats());
const historyList = computed(() => props.historyManager.getHistoryList());
const canGoBack = computed(() => props.historyManager.canGoBack());
const canGoForward = computed(() => props.historyManager.canGoForward());

// 方法
const goBack = () => {
  const previousState = props.historyManager.goBack();
  if (previousState) {
    emit('go-back', previousState);
    emit('state-changed', previousState);
  }
};

const goForward = () => {
  const nextState = props.historyManager.goForward();
  if (nextState) {
    emit('go-forward', nextState);
    emit('state-changed', nextState);
  }
};

const goToStep = (index) => {
  const targetState = props.historyManager.goToIndex(index);
  if (targetState) {
    emit('go-to-step', targetState, index);
    emit('state-changed', targetState);
    showHistoryPanel.value = false;
  }
};

const saveCurrentStep = () => {
  emit('save-step', props.currentState);
};

const toggleHistoryPanel = () => {
  showHistoryPanel.value = !showHistoryPanel.value;
};

const clearHistory = () => {
  if (confirm('確定要清除所有歷史記錄嗎？此操作無法撤銷。')) {
    props.historyManager.clear();
    emit('clear-history');
    showHistoryPanel.value = false;
  }
};

const formatTime = (timestamp) => {
  const date = new Date(timestamp);
  const now = new Date();
  const diffMs = now - date;
  const diffMins = Math.floor(diffMs / 60000);
  
  if (diffMins < 1) {
    return '剛剛';
  } else if (diffMins < 60) {
    return `${diffMins}分鐘前`;
  } else if (diffMins < 1440) {
    const hours = Math.floor(diffMins / 60);
    return `${hours}小時前`;
  } else {
    return date.toLocaleDateString('zh-TW', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  }
};
</script>

<style scoped>
.next-step-control {
  position: relative;
  display: flex;
  align-items: center;
  gap: 8px;
}

.step-buttons {
  display: flex;
  gap: 4px;
}

.step-btn {
  padding: 6px 8px;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  background: white;
  color: #374151;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.2s;
  min-width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.step-btn:hover:not(:disabled) {
  background: #f3f4f6;
  border-color: #9ca3af;
}

.step-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  background: #f9fafb;
}

.step-btn.active {
  background: #3b82f6;
  color: white;
  border-color: #3b82f6;
}

.back-btn:hover:not(:disabled) {
  background: #fef3c7;
  border-color: #f59e0b;
  color: #92400e;
}

.forward-btn:hover:not(:disabled) {
  background: #dcfce7;
  border-color: #16a34a;
  color: #166534;
}

.save-step-btn:hover:not(:disabled) {
  background: #dbeafe;
  border-color: #3b82f6;
  color: #1d4ed8;
}

.step-indicator {
  font-size: 12px;
  color: #6b7280;
  padding: 0 8px;
  white-space: nowrap;
}

.step-text {
  font-weight: 500;
}

.history-panel {
  position: absolute;
  top: 100%;
  right: 0;
  margin-top: 8px;
  background: white;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  min-width: 280px;
  max-width: 320px;
}

.history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #e5e7eb;
}

.history-header h4 {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  color: #374151;
}

.close-btn {
  background: none;
  border: none;
  color: #6b7280;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
}

.close-btn:hover {
  background: #f3f4f6;
  color: #374151;
}

.history-list {
  max-height: 240px;
  overflow-y: auto;
}

.history-item {
  display: flex;
  align-items: center;
  padding: 8px 16px;
  cursor: pointer;
  border-bottom: 1px solid #f3f4f6;
  transition: background 0.2s;
}

.history-item:hover {
  background: #f9fafb;
}

.history-item.current {
  background: #eff6ff;
  border-left: 3px solid #3b82f6;
}

.history-item-content {
  flex: 1;
}

.history-description {
  font-size: 13px;
  font-weight: 500;
  color: #374151;
  margin-bottom: 2px;
}

.history-time {
  font-size: 11px;
  color: #6b7280;
}

.history-indicator {
  color: #3b82f6;
  font-size: 12px;
  margin-left: 8px;
}

.history-actions {
  padding: 8px 16px;
  border-top: 1px solid #e5e7eb;
}

.clear-history-btn {
  width: 100%;
  padding: 6px 12px;
  background: #fef2f2;
  color: #dc2626;
  border: 1px solid #fecaca;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.2s;
}

.clear-history-btn:hover {
  background: #fee2e2;
  border-color: #fca5a5;
}
</style>
